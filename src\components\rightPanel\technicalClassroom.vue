<!-- 技术课堂 -->
<template>
  <CPanel>
    <template #header>技术课堂</template>
    <template #content>
      <div class="content">
        <div class="classroom-stats">
          <div class="center-circle">
            <div class="total-number">2563</div>
            <div class="total-label">累计培训人次</div>
          </div>
          <div class="stats-list">
            <div class="stat-item" v-for="(item, index) in statsList" :key="index">
              <div class="stat-color" :style="{ backgroundColor: item.color }"></div>
              <div class="stat-info">
                <span class="stat-name">{{ item.name }}</span>
                <span class="stat-percent">{{ item.percent }}%</span>
              </div>
            </div>
          </div>
        </div>
        <div class="chart-container" id="technical-chart"></div>
      </div>
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import CPanel from '@/components/common/CPanel.vue'

const statsList = ref([
  { name: '主题1', percent: 34, color: '#E83F46' },
  { name: '主题5', percent: 34, color: '#0DC869' },
  { name: '主题2', percent: 30, color: '#FF8D39' },
  { name: '主题6', percent: 30, color: '#00D4DC' },
  { name: '主题3', percent: 34, color: '#FFD339' },
  { name: '主题7', percent: 34, color: '#4F7DFF' },
  { name: '主题4', percent: 30, color: '#B4E61A' },
  { name: '主题8', percent: 30, color: '#AF48FF' }
])

const initChart = () => {
  const chartDom = document.getElementById('technical-chart')
  if (chartDom) {
    const myChart = echarts.init(chartDom)
    const option = {
      series: [{
        type: 'pie',
        radius: ['45%', '75%'],
        center: ['50%', '50%'],
        data: statsList.value.map(item => ({
          value: item.percent,
          name: item.name,
          itemStyle: { color: item.color }
        })),
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    }
    myChart.setOption(option)
  }
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>

<style lang="scss" scoped>
.content {
  height: 100%;
  display: flex;
  align-items: center;
  gap: 20px;
}

.classroom-stats {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;

  .center-circle {
    position: relative;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(0, 255, 255, 0.2) 0%, rgba(0, 255, 255, 0.05) 100%);
    border: 2px solid rgba(0, 255, 255, 0.3);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;

    .total-number {
      font-size: 24px;
      font-weight: bold;
      color: #00FFFF;
      text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
    }

    .total-label {
      font-size: 12px;
      color: rgba(255, 255, 255, 0.8);
      margin-top: 4px;
    }
  }

  .stats-list {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    width: 100%;

    .stat-item {
      display: flex;
      align-items: center;
      gap: 6px;

      .stat-color {
        width: 8px;
        height: 8px;
        border-radius: 2px;
      }

      .stat-info {
        display: flex;
        align-items: center;
        gap: 4px;

        .stat-name {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.8);
        }

        .stat-percent {
          font-size: 12px;
          color: #00FFFF;
          font-weight: bold;
        }
      }
    }
  }
}

.chart-container {
  width: 150px;
  height: 150px;
}
</style>
