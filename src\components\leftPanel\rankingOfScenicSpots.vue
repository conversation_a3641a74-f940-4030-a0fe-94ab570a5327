<!-- 技术课堂 -->
<template>
  <CPanel>
    <template #header>技术课堂</template>
    <template #content>
      <div class="chart-container">
        <!-- 左侧环形饼图 -->
        <div class="chart-left">
          <CEcharts :option="chartOption" />
        </div>
        <!-- 右侧图例 -->
        <div class="chart-right">
          <div class="legend-item" v-for="(item, index) in chartData" :key="index">
            <div class="legend-color" :style="{ backgroundColor: item.color }"></div>
            <span class="legend-name">{{ item.name }}</span>
            <span class="legend-value">{{ item.value }}%</span>
          </div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import CPanel from '@/components/common/CPanel.vue'
import CEcharts from '@/components/common/CEcharts.vue'
import { ref, computed } from 'vue'

// 假数据
const chartData = ref([
  { name: '主题1', value: 34, color: '#E83F46' },
  { name: '主题2', value: 30, color: '#FF8D39' },
  { name: '主题3', value: 34, color: '#0DC869' },
  { name: '主题4', value: 30, color: '#00D4DC' },
  { name: '主题5', value: 34, color: '#FFD339' },
  { name: '主题6', value: 30, color: '#4F7DFF' },
  { name: '主题7', value: 34, color: '#B4E61A' },
  { name: '主题8', value: 30, color: '#AF48FF' }
])

// 计算总数
const totalValue = computed(() => {
  return chartData.value.reduce((sum, item) => sum + item.value, 0)
})

// ECharts配置
const chartOption = computed(() => {
  return {
    series: [
      {
        name: '访问来源',
        type: 'pie',
        radius: ['60%', '90%'], // 内外半径，形成环形
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: false
          }
        },
        labelLine: {
          show: false
        },
        data: chartData.value.map(item => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: item.color
          }
        }))
      }
    ],
    graphic: [
      {
        type: 'text',
        left: 'center',
        top: 'center',
        style: {
          text: totalValue.value.toString(),
          fontSize: 32,
          fontWeight: 'bold',
          fill: '#fff',
          y: -10
        }
      },
      // {
      //   type: 'text',
      //   left: 'center',
      //   top: 'center',
      //   style: {
      //     text: '访问总人次',
      //     fontSize: 14,
      //     fill: '#83FFF2',
      //     y: 19
      //   }
      // }
    ]
  }
})
</script>
<style lang="scss" scoped>
.chart-container {
  display: flex;
  align-items: center;
  height: 140px;
  padding: 20px;
  gap: 20px;
}

.chart-left {
  flex: 1;
  height: 100%;
  position: relative;
}

.chart-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;

  .legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
  }

  .legend-name {
    color: #83FFF2;
    min-width: 40px;
  }

  .legend-value {
    color: #fff;
    font-weight: bold;
    margin-left: auto;
  }
}
</style>
